/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     challenge.h
 *  @brief    Challenge value validation module
 *
 *  This module provides challenge-response authentication functionality
 *  for secure access control in the NFC lock firmware.
 */

#ifndef _CHALLENGE_H_
#define _CHALLENGE_H_

// standard libs
#include "core_cm0.h"
#include <stdbool.h>

// Smack ROM lib
#include "rom_lib.h"

// Utility functions
#include "util.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Global challenge value used for authentication
 */
extern uint32_t challenge_value;

/**
 * @brief Flag indicating whether the challenge has been passed
 */
extern bool challenge_passed;

/**
 * @brief Process challenge validation
 * 
 * This function checks if the challenge has been passed and clears
 * the mailbox if not. It should be called before executing protected
 * functions.
 * 
 * @return true if challenge was passed, false otherwise
 */
bool challenge_process(void);

/**
 * @brief Validate challenge response
 * 
 * This function compares the provided challenge response with the
 * expected challenge value and sets the challenge_passed flag accordingly.
 * The response is read from mailbox content[1].
 */
void challenge_validate_response(void);

/**
 * @brief Generate new challenge value
 * 
 * This function generates a new challenge value based on random
 * calculations using the mailbox content. The generated challenge
 * value is stored in the global challenge_value variable.
 */
void challenge_generate_value(void);

#ifdef __cplusplus
}
#endif

#endif /* _CHALLENGE_H_ */
