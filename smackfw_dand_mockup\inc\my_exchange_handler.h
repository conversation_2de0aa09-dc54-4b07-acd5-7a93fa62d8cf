/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     my_exchange_handler.h
 *  @brief    Custom exchange handler for NFC lock firmware
 *
 *  This module provides a centralized handler for various exchange functions
 *  based on mailbox content[1] values.
 */

#ifndef _MY_EXCHANGE_HANDLER_H_
#define _MY_EXCHANGE_HANDLER_H_

// standard libs
#include "core_cm0.h"
#include <stdbool.h>

// Smack ROM lib
#include "rom_lib.h"

#ifdef __cplusplus
extern "C" {
#endif

// Function codes for mailbox content[1]
#define MY_EXCHANGE_FUNC_CONFIG_MODIFY      0x01    // Modify my_config values
#define MY_EXCHANGE_FUNC_SU_KEY_READ        0x02    // Read SU key
#define MY_EXCHANGE_FUNC_RF_STATUS          0x03    // Get RF field status and RSSI/VCCCA
#define MY_EXCHANGE_FUNC_SENSOR_READ        0x04    // Read sensor values

/**
 * @brief Main exchange handler function
 *
 * This function processes the mailbox content[1] value to determine which
 * specific function to call. It replaces the individual app_func_3, 11, 12, 13
 * functions with a unified handler.
 *
 * @return void
 */
void my_exchange_handler(void);

/**
 * @brief Handle config modification (content[1] = 0x01)
 *
 * Modifies my_config values based on mailbox content[2-9].
 * Equivalent to original app_func_3 functionality.
 */
void handle_config_modify(void);

/**
 * @brief Handle SU key read (content[1] = 0x02)
 *
 * Reads the SU key from config_device and copies to mailbox.
 * Equivalent to original app_func_11 functionality.
 */
void handle_su_key_read(void);

/**
 * @brief Handle RF status read (content[1] = 0x03)
 *
 * Checks RF field presence and reads RSSI/VCCCA values.
 * Equivalent to original app_func_12 functionality.
 */
void handle_rf_status(void);

/**
 * @brief Handle sensor read (content[1] = 0x04)
 *
 * Reads sensor values (x, y, z, temperature) and target status.
 * Equivalent to original app_func_13 functionality.
 */
void handle_sensor_read(void);

#ifdef __cplusplus
}
#endif

#endif /* _MY_EXCHANGE_HANDLER_H_ */