# Challenge 控制功能说明

## 概述

在 NFC 锁固件中，challenge 验证是一个重要的安全机制，用于确保只有经过验证的请求才能执行受保护的功能。为了方便开发和调试，我们添加了一个编译时开关来控制是否启用 challenge 验证。

## 配置方法

在 `inc/settings.h` 文件中，可以通过 `CHALLENGE_ENABLE` 宏来控制 challenge 验证：

```c
// 启用 challenge 验证（默认，生产环境推荐）
#define CHALLENGE_ENABLE    1

// 禁用 challenge 验证（开发/调试时使用）
#define CHALLENGE_ENABLE    0
```

## 行为说明

### 启用状态 (`CHALLENGE_ENABLE = 1`)

- `challenge_is_passed()` 函数按正常逻辑工作
- 只有当 `challenge_passed` 为 true 时才返回 true
- 如果验证失败，会清空 mailbox 内容并返回 false
- 这是生产环境的推荐设置

### 禁用状态 (`CHALLENGE_ENABLE = 0`)

- `challenge_is_passed()` 函数始终返回 true
- 跳过所有 challenge 验证逻辑
- 不会清空 mailbox 内容
- 适用于开发和调试阶段

## 使用场景

### 开发阶段
```c
#define CHALLENGE_ENABLE    0  // 禁用验证，方便测试
```

在开发阶段，可以禁用 challenge 验证来：
- 简化测试流程
- 快速验证功能逻辑
- 调试数据交换功能

### 生产环境
```c
#define CHALLENGE_ENABLE    1  // 启用验证，确保安全
```

在生产环境中，必须启用 challenge 验证来：
- 确保安全性
- 防止未授权访问
- 保护敏感功能

## 代码影响

当 `CHALLENGE_ENABLE = 0` 时，编译器会优化掉不需要的代码，从而：
- 减少代码大小（约 40 字节）
- 提高执行效率
- 简化调试过程

## 注意事项

1. **安全警告**: 在生产环境中绝对不要禁用 challenge 验证
2. **编译优化**: 修改此设置后需要重新编译整个项目
3. **测试建议**: 在发布前务必测试启用 challenge 的版本
4. **文档同步**: 确保团队成员了解当前的 challenge 设置状态

## 验证方法

可以通过以下方式验证 challenge 控制是否正常工作：

1. **代码大小检查**: 禁用时 `challenge.o` 应该比启用时小约 40 字节
2. **功能测试**: 禁用时所有 `my_exchange_handler` 功能应该无需 challenge 即可执行
3. **安全测试**: 启用时未通过 challenge 的请求应该被拒绝

## 相关文件

- `inc/settings.h` - 配置 `CHALLENGE_ENABLE` 宏
- `src/challenge.c` - 实现 challenge 控制逻辑
- `src/my_exchange_handler.c` - 使用 `challenge_is_passed()` 进行验证
