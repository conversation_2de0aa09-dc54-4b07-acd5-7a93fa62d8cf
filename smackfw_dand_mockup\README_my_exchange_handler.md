# My Exchange Handler 说明

## 概述

`my_exchange_handler` 是一个自定义的数据交换处理模块，提供了统一的接口来处理各种数据交换功能。通过调用 `app_func_10` 并设置 mailbox 的 `content[1]` 值来指定要执行的具体功能。

## 文件结构

- `inc/my_exchange_handler.h` - 头文件，定义了函数常量和函数声明
- `src/my_exchange_handler.c` - 实现文件，包含所有处理逻辑
- `src/smack_dand_mockup.c` - `app_func_10` 调用新的处理器

## 支持的功能

1. **配置修改** (`content[1] = 0x01`)
   - 常量: `MY_EXCHANGE_FUNC_CONFIG_MODIFY`
   - 功能: 从 mailbox `content[2-9]` 读取值并更新 `my_config.value1-8`

2. **用户密钥擦除** (`content[1] = 0x02`)
   - 常量: `MY_EXCHANGE_FUNC_USER_KEY_ERASE`
   - 功能: 验证提供的 SU key（管理员密码），如果正确则擦除用户密钥
   - 输入: `content[2-5]` 包含 SU key (16 字节，4个 uint32_t)
   - 输出: `content[2]` = 1 表示成功，0 表示失败

3. **RF 状态检测** (`content[1] = 0x03`)
   - 常量: `MY_EXCHANGE_FUNC_RF_STATUS`
   - 功能: 检测 RF 场强并读取 RSSI/VCCCA 值
   - 输出: `content[2]` = RSSI 值, `content[3]` = VCCCA 值

4. **传感器读取** (`content[1] = 0x04`)
   - 常量: `MY_EXCHANGE_FUNC_SENSOR_READ`
   - 功能: 读取传感器值 (x, y, z, 温度) 和目标状态
   - 输出: `content[2-6]` 分别为 x, y, z, 温度, 目标到达状态

## 使用示例

### 修改配置值
```c
Mailbox_t *mbox = get_mailbox_address();
mbox->content[1] = MY_EXCHANGE_FUNC_CONFIG_MODIFY;  // 0x01
mbox->content[2] = new_value1;
mbox->content[3] = new_value2;
// ... 设置 content[4-9] 为其他值
app_func_10();  // 调用处理器
```

### 擦除用户密钥
```c
Mailbox_t *mbox = get_mailbox_address();
mbox->content[1] = MY_EXCHANGE_FUNC_USER_KEY_ERASE;  // 0x02
// 设置 SU key (16 字节，分为 4 个 uint32_t)
mbox->content[2] = su_key_part1;
mbox->content[3] = su_key_part2;
mbox->content[4] = su_key_part3;
mbox->content[5] = su_key_part4;
app_func_10();  // 调用处理器
// mbox->content[2] = 1 表示成功擦除，0 表示 SU key 验证失败
```

### 检测 RF 状态
```c
Mailbox_t *mbox = get_mailbox_address();
mbox->content[1] = MY_EXCHANGE_FUNC_RF_STATUS;  // 0x03
app_func_10();  // 调用处理器
// mbox->content[2] = RSSI 值
// mbox->content[3] = VCCCA 值
```

### 读取传感器
```c
Mailbox_t *mbox = get_mailbox_address();
mbox->content[1] = MY_EXCHANGE_FUNC_SENSOR_READ;  // 0x04
app_func_10();  // 调用处理器
// mbox->content[2] = x 轴值
// mbox->content[3] = y 轴值
// mbox->content[4] = z 轴值
// mbox->content[5] = 温度值
// mbox->content[6] = 目标到达状态
```

## 安全性

所有函数都会首先调用 `challenge_process()` 进行安全验证，只有通过验证的请求才会被处理。

用户密钥擦除功能需要提供正确的 SU key（管理员密码）才能执行，确保只有授权用户才能擦除用户密钥。

## 扩展性

要添加新的功能，只需要：
1. 在 `my_exchange_handler.h` 中定义新的常量（如 `MY_EXCHANGE_FUNC_NEW_FEATURE = 0x05`）
2. 在 `my_exchange_handler.c` 的 switch 语句中添加新的 case
3. 实现对应的处理函数

## 构建状态

✅ 代码已成功编译，没有错误
