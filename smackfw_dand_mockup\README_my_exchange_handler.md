# My Exchange Handler 重构说明

## 概述

本次重构将原来的 `app_func_3`, `app_func_11`, `app_func_12`, `app_func_13` 函数的逻辑整合到了一个统一的 `my_exchange_handler` 模块中。现在只需要调用 `app_func_10` 并通过 mailbox 的 `content[1]` 值来指定要执行的具体功能。

## 文件结构

- `inc/my_exchange_handler.h` - 头文件，定义了函数常量和函数声明
- `src/my_exchange_handler.c` - 实现文件，包含所有处理逻辑
- `src/smack_dand_mockup.c` - 修改了 `app_func_10` 来调用新的处理器

## 功能映射

### 原来的函数 -> 新的调用方式

1. **app_func_3** (修改 my_config 值)
   - 新调用方式: `app_func_10` + `content[1] = 0x01`
   - 常量: `MY_EXCHANGE_FUNC_CONFIG_MODIFY`
   - 功能: 从 mailbox `content[2-9]` 读取值并更新 `my_config.value1-8`

2. **app_func_11** (读取 SU key)
   - 新调用方式: `app_func_10` + `content[1] = 0x02`
   - 常量: `MY_EXCHANGE_FUNC_SU_KEY_READ`
   - 功能: 读取 `config_device.su_key` 并复制到 mailbox

3. **app_func_12** (RF 状态检测)
   - 新调用方式: `app_func_10` + `content[1] = 0x03`
   - 常量: `MY_EXCHANGE_FUNC_RF_STATUS`
   - 功能: 检测 RF 场强并读取 RSSI/VCCCA 值

4. **app_func_13** (传感器读取)
   - 新调用方式: `app_func_10` + `content[1] = 0x04`
   - 常量: `MY_EXCHANGE_FUNC_SENSOR_READ`
   - 功能: 读取传感器值 (x, y, z, 温度) 和目标状态

## 使用示例

### 修改配置值 (原 app_func_3)
```c
Mailbox_t *mbox = get_mailbox_address();
mbox->content[1] = MY_EXCHANGE_FUNC_CONFIG_MODIFY;  // 0x01
mbox->content[2] = new_value1;
mbox->content[3] = new_value2;
// ... 设置 content[4-9] 为其他值
app_func_10();  // 调用处理器
```

### 读取 SU Key (原 app_func_11)
```c
Mailbox_t *mbox = get_mailbox_address();
mbox->content[1] = MY_EXCHANGE_FUNC_SU_KEY_READ;  // 0x02
app_func_10();  // 调用处理器
// SU key 将被复制到 mbox->content[2] 开始的位置
```

### 检测 RF 状态 (原 app_func_12)
```c
Mailbox_t *mbox = get_mailbox_address();
mbox->content[1] = MY_EXCHANGE_FUNC_RF_STATUS;  // 0x03
app_func_10();  // 调用处理器
// mbox->content[2] = RSSI 值
// mbox->content[3] = VCCCA 值
```

### 读取传感器 (原 app_func_13)
```c
Mailbox_t *mbox = get_mailbox_address();
mbox->content[1] = MY_EXCHANGE_FUNC_SENSOR_READ;  // 0x04
app_func_10();  // 调用处理器
// mbox->content[2] = x 轴值
// mbox->content[3] = y 轴值
// mbox->content[4] = z 轴值
// mbox->content[5] = 温度值
// mbox->content[6] = 目标到达状态
```

## 安全性

所有函数都会首先调用 `challenge_process()` 进行安全验证，只有通过验证的请求才会被处理。

## 向后兼容性

原来的 `app_func_3`, `app_func_11`, `app_func_12`, `app_func_13` 函数仍然存在但已被注释掉，并添加了说明如何使用新的接口。如果需要，可以取消注释来恢复原来的行为。

## 扩展性

要添加新的功能，只需要：
1. 在 `my_exchange_handler.h` 中定义新的常量
2. 在 `my_exchange_handler.c` 的 switch 语句中添加新的 case
3. 实现对应的处理函数

## 构建状态

✅ 代码已成功编译，没有错误
⚠️ 修复了一个关于指针类型的警告
