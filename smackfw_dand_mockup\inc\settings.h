/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/*
 * settings.h
 *
 *  Created on: 20.11.2020
 *      Author: hs
 */

#ifndef INC_SETTINGS_H_
#define INC_SETTINGS_H_


#include "version.h"

//=================================================================
// Global definitions

// hardware variants encoded in last digit of build number
#define BOARD_EVAL_V22          48022
#define BOARD_BOOST_BUCK_V30    48030
#define BOARD_EVAL_V50_MINI     48050

#if (FIRMWARE_VERSION_BUILD % 10) == 1
#define SMACK_BOARD             BOARD_EVAL_V22
#elif (FIRMWARE_VERSION_BUILD % 10) == 2
#define SMACK_BOARD             BOARD_BOOST_BUCK_V30
#elif (FIRMWARE_VERSION_BUILD % 10) == 3
#define SMACK_BOARD             BOARD_EVAL_V50_MINI
#else
#error unknown hardware selected
#endif

//=================================================================
// hardware settings

#if SMACK_BOARD == BOARD_EVAL_V22

// GPIO pin where the LED is connected to
#define GPIO_LED1       0


// show a Hardfault event by toggling a GPIO pin (undef to disable)
// in case of a Hardfault event, the PIN is toggled for a whule before issuing a reset
//#define DBG_GPIO_HARDFAULT      (1)

// show a call to the Smack exchange handler on a GPIO pin (undef to disable)
//#define DBG_GPIO_SMACK_EXCHANGE (3)

// show the startup time on a GPIO pin (undef to disable)
// The GPIO is set when the NVM user function _nvm_start() is entered. After a short period,
// the GPIO is reset (or not?!?)
//#define DBG_GPIO_SMACK_START    (4)


#elif SMACK_BOARD == BOARD_BOOST_BUCK_V30

// GPIO pin where the LED is connected to
#define GPIO_LED1       2


// show a Hardfault event by toggling a GPIO pin (undef to disable)
// in case of a Hardfault event, the PIN is toggled for a whule before issuing a reset
//#define DBG_GPIO_HARDFAULT      (1)

// show a call to the Smack exchange handler on a GPIO pin (undef to disable)
//#define DBG_GPIO_SMACK_EXCHANGE (3)

// show the startup time on a GPIO pin (undef to disable)
// The GPIO is set when the NVM user function _nvm_start() is entered. After a short period,
// the GPIO is reset (or not?!?)
//#define DBG_GPIO_SMACK_START    (4)

#elif SMACK_BOARD == BOARD_EVAL_V50_MINI

// GPIO pin where the LED is connected to
#define GPIO_LED1       2


// show a Hardfault event by toggling a GPIO pin (undef to disable)
// in case of a Hardfault event, the PIN is toggled for a whule before issuing a reset
//#define DBG_GPIO_HARDFAULT      (1)

// show a call to the Smack exchange handler on a GPIO pin (undef to disable)
//#define DBG_GPIO_SMACK_EXCHANGE (3)

// show the startup time on a GPIO pin (undef to disable)
// The GPIO is set when the NVM user function _nvm_start() is entered. After a short period,
// the GPIO is reset (or not?!?)
//#define DBG_GPIO_SMACK_START    (4)


#else
#error hardware not set
#endif

#if defined DEBUG && DEBUG
#if GPIO_LED1 == 2
#warning disabling LED, using GPIO as UART
#undef GPIO_LED1
#endif
#endif

//=================================================================
// system settings

#define VCCHB_CLAMP_RAW (3300U)


//=================================================================
// Smack smartlock demo:
// This demo may support different methods to drive the motor. e.g. do one turn, or
// move it stepwise in order to recharge the capacitor which provides the energy for
// motor operation between the steps.
// See motor_method_e for configurable methods.
#define MOTOR_CONTROL_METHOD    motor_stepwise_voltage


//-----------------------------------------------------------------
// Global settings

// total time the motor shall run
// number of cycles will be calculated from configured timing or from actual measurement (e.g. when using voltage controlled strategy)
#define TOTAL_MOTOR_RUNTIME     1500


//-----------------------------------------------------------------
// Settings for voltage controlled operations

// specify voltage levels in millivolts (add about 2% or 3% for a better match of the prescaler)
#define STEPWISE_VOLT_START     3000
#define STEPWISE_VOLT_STOP      2200


//-----------------------------------------------------------------
// Settings for timer controlled operations

// specify voltage levels in millivolts (add about 2% or 3% for a better match of the prescaler)
#define STEPWISE_TIME_ON        300
#define STEPWISE_TIME_OFF       300


//-----------------------------------------------------------------
// Settings for one shot operation

// voltage level in millivolts at which motor operation starts
#define ONESHOT_VOLT_START      3000


//=================================================================
// features



#endif /* INC_SETTINGS_H_ */
